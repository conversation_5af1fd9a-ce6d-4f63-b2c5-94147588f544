/*
        _.-- ,.--.
      .'   .'     /
       @       |'..--------._
     /      \._/              '.
    /  .-.-                     \
   (  /    \                     \
   \      '.                  | #
    \       \   -.           /
     :\       |    )._____.'   \
      "       |   /  \  |  \    )
    Kduckp       |   |./'  :__ \.-'
              '--'
*/
/*
 * Author: Kduckp
 * Generated on: 2025-07-01 18:07:55
*/

#include <bits/stdc++.h>
using namespace std;

#define int long long
#define fast ios_base::sync_with_stdio(0); cin.tie(0); cout.tie(0);
#define __TOISETHIVOI__ signed main()

void init() {
  freopen("input.inp", "r", stdin);
  freopen("output.out", "w", stdout);
  fast;
}

__TOISETHIVOI__ {
  init();
  int n; cin >> n;
  map<int, int> l, r;
  for (int i = 0, x; i < 2 * n - 1; i++) {
    cin >> x;
    int v = abs(x);
    if (x < 0) l[v]++;
    if (x > 0) r[v]++;
  }
  for (auto &i : l) {
    if (i.second != r[i.first]) {
      cout << 1 << endl << i.first;
      return 0;
    }
  }
  for (auto &i : r) {
    if (i.second != l[i.first]) {
      cout << -1 << endl << i.first;
      return 0;
    }
  }
  return 0;
}
